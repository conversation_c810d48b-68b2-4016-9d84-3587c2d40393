# Check this guide for more information about publishing to ghcr.io with GitHub Actions:
# https://docs.github.com/en/packages/managing-github-packages-using-github-actions-workflows/publishing-and-installing-a-package-with-github-actions#upgrading-a-workflow-that-accesses-ghcrio

# Build the Docker image and push it to the registry
name: docker_publish

on:
  # Trigger the workflow on tags push that match the pattern v*, for example v1.0.0
  push:
    tags:
      - "v*"

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  # Only run this job on tags
  docker-tag:
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    # Sets the permissions granted to the GITHUB_TOKEN for the actions in this job.
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      # We require additional space due to the large size of our image. (~10GB)
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: true
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          docker-images: true
          swap-storage: true

      - name: Docker meta:${{ github.ref_name }}
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository_owner }}/kohya-ss-gui
          flavor: |
            latest=auto
            prefix=
            suffix=
          # https://github.com/docker/metadata-action/tree/v5/?tab=readme-ov-file#tags-input
          tags: |
            type=semver,pattern=v{{major}}
            type=semver,pattern={{raw}}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # You may need to manage write and read access of GitHub Actions for repositories in the container settings.
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v6
        id: publish
        with:
          context: .
          file: ./Dockerfile
          push: true
          target: final
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VERSION=${{ github.ref_name }}
            RELEASE=${{ github.run_number }}
          platforms: linux/amd64
          # Cache to regietry instead of gha to avoid the capacity limit.
          cache-from: type=registry,ref=ghcr.io/${{ github.repository_owner }}/kohya-ss-gui:cache
          cache-to: type=registry,ref=ghcr.io/${{ github.repository_owner }}/kohya-ss-gui:cache,mode=max
          sbom: true
          provenance: true
