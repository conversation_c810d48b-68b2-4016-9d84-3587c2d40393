ignored:
  - DL3042 # Avoid use of cache directory with pip. Use `pip install --no-cache-dir <package>`
  - DL3013 # Pin versions in pip. Instead of `pip install <package>` use `pip install <package>==<version>`
  - DL3008 # Pin versions in apt get install. Instead of `apt-get install <package>` use `apt-get install <package>=<version>`
  - DL4006 # Set the SHELL option -o pipefail before RUN with a pipe in it
  - SC2015 # Note that A && B || C is not if-then-else. C may run when A is true.