services:
  kohya-ss-gui:
    container_name: kohya-ss-gui
    image: ghcr.io/bmaltais/kohya-ss-gui:latest
    user: 1000:0
    build:
      context: .
      args:
        - UID=1000
      cache_from:
        - ghcr.io/bmaltais/kohya-ss-gui:cache
      cache_to:
        - type=inline
    ports:
      - 7860:7860
    environment:
      SAFETENSORS_FAST_GPU: 1
      TENSORBOARD_PORT: ${TENSORBOARD_PORT:-6006}
    tmpfs:
      - /tmp
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix
      - ./models:/app/models
      - ./dataset:/dataset
      - ./dataset/images:/app/data
      - ./dataset/logs:/app/logs
      - ./dataset/outputs:/app/outputs
      - ./dataset/regularization:/app/regularization
      - ./models:/app/models
      - ./.cache/config:/app/config
      - ./.cache/user:/home/<USER>/.cache
      - ./.cache/triton:/home/<USER>/.triton
      - ./.cache/nv:/home/<USER>/.nv
      - ./.cache/keras:/home/<USER>/.keras
      - ./.cache/config:/home/<USER>/.config # For backward compatibility
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ["all"]

  tensorboard:
    container_name: tensorboard
    image: tensorflow/tensorflow:latest-gpu
    ports:
      # !Please change the port in .env file
      - ${TENSORBOARD_PORT:-6006}:6006
    volumes:
      - ./dataset/logs:/app/logs
    command: tensorboard --logdir=/app/logs --bind_all
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              capabilities: [gpu]
              device_ids: ["all"]
