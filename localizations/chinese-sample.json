{"Loading...": "载入中...", "Use via API": "通过API使用", "Built with Gradio": "使用Gradio构建", "Dreambooth": "梦想阁", "Training": "训练", "Train a custom model using kohya dreambooth python code…": "使用kohya dreamboot python代码 训练个性化模型", "Configuration file": "配置文件", "Open": "打开", "Save": "保存", "Load": "加载", "Source model": "模型来源", "Model Quick Pick": "快速选择模型", "Save trained model as": "保存训练模型为", "Folders": "文件夹", "Start training": "开始训练", "Stop training": "停止训练", "Print training command": "打印训练命令", "Start tensorboard": "开始 tensorboard", "Stop tensorboard": "结束 tensorboard", "Image folder": "图片文件夹", "Regularisation folder": "正则化文件夹", "Output folder": "输出文件夹", "Logging folder": "日志文件夹", "Model output name": "模型输出文件夹", "Training comment": "训练注释", "(Optional) Add training comment to be included in metadata": "(可选)增加训练注释到元数据", "Parameters": "参数", "Basic": "基础", "Train batch size": "训练批次大小", "Epoch": "数量增加", "Max train epoch": "每批数量", "(Optional) Enforce number of epoch": "(可选)强制每批数量", "Advanced": "增强", "Samples": "样例", "Tools": "工具", "This section provide Dreambooth tools to help setup your dataset…": "这些选择帮助设置自己的数据集", "Dreambooth/LoRA Folder preparation": "Dreambooth/LoRA文件准备", "This utility will create the necessary folder structure for the training images and optional regularization images needed for the kohys_ss Dreambooth/LoRA method to function correctly.": "为训练文件创建文件夹", "Instance prompt": "实例提示", "Class prompt": "类提示", "Training images": "训练图片", "Directory containing the training images": "直接包含训练图片", "Repeats": "重复", "Regularisation images": "正则化图像", "Destination training directory": "训练结果目录", "Directory where formatted training and regularisation folders will be placed": "训练和正则化文件会被取代", "Prepare training data": "准备训练数据", "Copy info to Folders Tab": "复制信息到文件夹", "Train a custom model using kohya train network LoRA python code…": "使用kohya训练网络LoRA训练个性化模型"}