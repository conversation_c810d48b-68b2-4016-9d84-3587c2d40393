{"adaptive_noise_scale": 0, "additional_parameters": "", "async_upload": false, "bucket_no_upscale": true, "bucket_reso_steps": 64, "cache_latents": true, "cache_latents_to_disk": true, "caption_dropout_every_n_epochs": 0, "caption_dropout_rate": 0, "caption_extension": ".txt", "clip_g": "H:/ComfyUI2/models/clip/clip_g.safetensors", "clip_l": "H:/ComfyUI2/models/clip/clip_l.safetensors", "clip_skip": 1, "color_aug": false, "dataset_config": "", "debiased_estimation_loss": false, "disable_mmap_load_safetensors": false, "dynamo_backend": "no", "dynamo_mode": "default", "dynamo_use_dynamic": false, "dynamo_use_fullgraph": false, "enable_bucket": true, "epoch": 8, "extra_accelerate_launch_args": "", "flip_aug": false, "full_bf16": false, "full_fp16": false, "fused_backward_pass": false, "fused_optimizer_groups": 0, "gpu_ids": "", "gradient_accumulation_steps": 1, "gradient_checkpointing": true, "huber_c": 0.1, "huber_schedule": "snr", "huggingface_path_in_repo": "", "huggingface_repo_id": "", "huggingface_repo_type": "", "huggingface_repo_visibility": "", "huggingface_token": "", "ip_noise_gamma": 0, "ip_noise_gamma_random_strength": false, "keep_tokens": 0, "learning_rate": 5e-06, "learning_rate_te": 0, "learning_rate_te1": 1e-05, "learning_rate_te2": 1e-05, "log_config": false, "log_tracker_config": "", "log_tracker_name": "", "log_with": "", "logging_dir": "C:/Users/<USER>/Downloads/martini/logs/sd3", "logit_mean": 0, "logit_std": 1, "loss_type": "l2", "lr_scheduler": "cosine", "lr_scheduler_args": "", "lr_scheduler_num_cycles": 1, "lr_scheduler_power": 1, "lr_scheduler_type": "", "lr_warmup": 10, "main_process_port": 0, "masked_loss": false, "max_bucket_reso": 1536, "max_data_loader_n_workers": 0, "max_resolution": "512,512", "max_timestep": 1000, "max_token_length": 225, "max_train_epochs": 8, "max_train_steps": 1600, "mem_eff_attn": false, "metadata_author": "", "metadata_description": "", "metadata_license": "", "metadata_tags": "", "metadata_title": "", "min_bucket_reso": 256, "min_snr_gamma": 0, "min_timestep": 0, "mixed_precision": "bf16", "mode_scale": 1.29, "model_list": "custom", "multi_gpu": false, "multires_noise_discount": 0.3, "multires_noise_iterations": 0, "no_token_padding": false, "noise_offset": 0, "noise_offset_random_strength": false, "noise_offset_type": "Original", "num_cpu_threads_per_process": 2, "num_machines": 1, "num_processes": 1, "optimizer": "PagedAdamW8bit", "optimizer_args": "weight_decay=0.1 betas=.9,.95", "output_dir": "E:/models/sd3", "output_name": "sd3", "persistent_data_loader_workers": false, "pretrained_model_name_or_path": "E:/models/sd3/sd3_medium.safetensors", "prior_loss_weight": 1, "random_crop": false, "reg_data_dir": "", "resume": "", "resume_from_huggingface": "", "sample_every_n_epochs": 0, "sample_every_n_steps": 0, "sample_prompts": "", "sample_sampler": "euler_a", "save_as_bool": false, "save_clip": false, "save_every_n_epochs": 0, "save_every_n_steps": 0, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_model_as": "safetensors", "save_precision": "fp16", "save_state": false, "save_state_on_train_end": false, "save_state_to_huggingface": false, "save_t5xxl": false, "scale_v_pred_loss_like_noise_pred": false, "sd3_cache_text_encoder_outputs": true, "sd3_cache_text_encoder_outputs_to_disk": true, "sd3_checkbox": true, "sd3_text_encoder_batch_size": 1, "sdxl": false, "sdxl_cache_text_encoder_outputs": false, "sdxl_no_half_vae": false, "seed": 1026, "shuffle_caption": false, "stop_text_encoder_training": 0, "t5xxl": "H:/ComfyUI2/models/clip/t5xxl_fp8_e4m3fn.safetensors", "t5xxl_device": "", "t5xxl_dtype": "bf16", "train_batch_size": 1, "train_data_dir": "C:/Users/<USER>/Downloads/martini/img2", "v2": false, "v_parameterization": false, "v_pred_like_loss": 0, "vae": "", "vae_batch_size": 0, "wandb_api_key": "", "wandb_run_name": "", "weighted_captions": false, "weighting_scheme": "logit_normal", "xformers": "sdpa"}