{"adaptive_noise_scale": 0.00375, "additional_parameters": "", "batch_size": "4", "block_lr": "", "bucket_no_upscale": true, "bucket_reso_steps": 64, "cache_latents": true, "cache_latents_to_disk": false, "caption_dropout_every_n_epochs": 0.0, "caption_dropout_rate": 0, "caption_extension": ".txt", "clip_skip": "1", "color_aug": false, "create_buckets": true, "create_caption": true, "dataset_repeats": "1", "epoch": 240, "flip_aug": false, "full_bf16": true, "full_fp16": false, "full_path": true, "gradient_accumulation_steps": 6.0, "gradient_checkpointing": true, "keep_tokens": "0", "learning_rate": 5e-05, "lr_scheduler": "constant", "lr_scheduler_args": "", "lr_warmup": 0, "max_bucket_reso": "1024", "max_data_loader_n_workers": "0", "max_resolution": "1024,1024", "max_timestep": 900, "max_token_length": "75", "max_train_epochs": "240", "mem_eff_attn": false, "min_bucket_reso": "64", "min_snr_gamma": 5, "min_timestep": 100, "mixed_precision": "bf16", "multires_noise_discount": 0, "multires_noise_iterations": 0, "noise_offset": 0.0375, "noise_offset_type": "Original", "num_cpu_threads_per_process": 2, "optimizer": "PagedAdamW8bit", "optimizer_args": "", "persistent_data_loader_workers": false, "random_crop": false, "save_every_n_epochs": 240, "save_every_n_steps": 0, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_precision": "bf16", "scale_v_pred_loss_like_noise_pred": false, "sdxl_cache_text_encoder_outputs": true, "sdxl_checkbox": true, "sdxl_no_half_vae": true, "seed": "1234", "shuffle_caption": false, "train_batch_size": 2, "train_text_encoder": false, "use_latent_files": "No", "log_with": "", "v2": false, "v_parameterization": false, "v_pred_like_loss": 0, "vae_batch_size": 0, "weighted_captions": false, "xformers": "xformers"}