{"adaptive_noise_scale": 0, "additional_parameters": "", "batch_size": "1", "block_lr": "", "bucket_no_upscale": false, "bucket_reso_steps": 64, "cache_latents": true, "cache_latents_to_disk": true, "caption_dropout_every_n_epochs": 0.0, "caption_dropout_rate": 0, "caption_extension": ".txt", "caption_metadata_filename": "meta_cap.json", "clip_skip": "1", "color_aug": false, "dataset_repeats": "1", "epoch": 1, "flip_aug": false, "full_bf16": false, "full_fp16": false, "full_path": true, "generate_caption_database": true, "generate_image_buckets": true, "gradient_accumulation_steps": 1.0, "gradient_checkpointing": true, "image_folder": "/kohya_ss/dataset/1_/", "keep_tokens": 0, "latent_metadata_filename": "meta_lat.json", "learning_rate": 1e-06, "logging_dir": "/kohya_ss/output/SDXL1.0_Essenz-series-by-AI_Characters_Concept_Morphing-v1.0", "lr_scheduler": "constant", "lr_scheduler_args": "", "lr_warmup": 0, "max_bucket_reso": "4096", "max_data_loader_n_workers": "0", "max_resolution": "1024,1024", "max_timestep": 1000, "max_token_length": "75", "max_train_epochs": "100", "mem_eff_attn": false, "min_bucket_reso": "64", "min_snr_gamma": 0, "min_timestep": 0, "mixed_precision": "fp16", "model_list": "stabilityai/stable-diffusion-xl-base-1.0", "multires_noise_discount": 0, "multires_noise_iterations": 0, "noise_offset": 0, "noise_offset_type": "Original", "num_cpu_threads_per_process": 2, "optimizer": "AdamW8bit", "optimizer_args": "", "output_dir": "/kohya_ss/output/SDXL1.0_Essenz-series-by-AI_Characters_Concept_Morphing-v1.0", "output_name": "SDXL1.0_Essenz-series-by-AI_Characters_Concept_Morphing-v1.0", "persistent_data_loader_workers": false, "pretrained_model_name_or_path": "stabilityai/stable-diffusion-xl-base-1.0", "random_crop": false, "resume": "", "sample_every_n_epochs": 0, "sample_every_n_steps": 0, "sample_prompts": "", "sample_sampler": "k_dpm_2", "save_every_n_epochs": 10, "save_every_n_steps": 0, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_model_as": "safetensors", "save_precision": "fp16", "save_state": false, "scale_v_pred_loss_like_noise_pred": false, "sdxl_cache_text_encoder_outputs": false, "sdxl_checkbox": true, "sdxl_no_half_vae": true, "seed": "", "shuffle_caption": false, "train_batch_size": 1, "train_dir": "/kohya_ss/output/SDXL1.0_Essenz-series-by-AI_Characters_Concept_Morphing-v1.0", "train_text_encoder": true, "use_latent_files": "Yes", "log_with": "", "v2": false, "v_parameterization": false, "v_pred_like_loss": 0, "vae_batch_size": 0, "wandb_api_key": "", "weighted_captions": false, "xformers": "xformers"}