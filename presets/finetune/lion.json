{"batch_size": "1", "bucket_no_upscale": true, "bucket_reso_steps": 1.0, "cache_latents": true, "caption_dropout_every_n_epochs": 0.0, "caption_dropout_rate": 0.1, "caption_extension": ".txt", "clip_skip": 1, "color_aug": false, "create_buckets": false, "create_caption": true, "dataset_repeats": "10", "epoch": "2", "flip_aug": false, "full_fp16": false, "full_path": true, "gradient_accumulation_steps": 1.0, "gradient_checkpointing": false, "keep_tokens": 1, "learning_rate": "0.0000166666666", "lr_scheduler": "cosine", "lr_warmup": "10", "max_bucket_reso": "1024", "max_data_loader_n_workers": "0", "max_resolution": "512,512", "max_token_length": "150", "max_train_epochs": "", "mem_eff_attn": false, "min_bucket_reso": "256", "mixed_precision": "bf16", "noise_offset": "", "num_cpu_threads_per_process": 2, "optimizer": "Lion", "optimizer_args": "", "persistent_data_loader_workers": false, "random_crop": false, "save_every_n_epochs": "1", "save_precision": "fp16", "seed": "1234", "shuffle_caption": true, "train_batch_size": 4, "train_text_encoder": true, "use_8bit_adam": false, "use_latent_files": "No", "v2": false, "v_parameterization": false, "xformers": true}