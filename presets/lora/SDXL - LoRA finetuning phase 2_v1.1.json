{"LoRA_type": "Standard", "adaptive_noise_scale": 0.00357, "additional_parameters": "--log_prefix=xl-loha", "block_alphas": "", "block_dims": "", "block_lr_zero_threshold": "", "bucket_no_upscale": false, "bucket_reso_steps": 32, "cache_latents": true, "cache_latents_to_disk": true, "caption_dropout_every_n_epochs": 0.0, "caption_dropout_rate": 0, "caption_extension": ".txt2", "clip_skip": "1", "color_aug": false, "conv_alpha": 4, "conv_alphas": "", "conv_dim": 4, "conv_dims": "", "decompose_both": false, "dim_from_weights": true, "down_lr_weight": "", "enable_bucket": true, "epoch": 1, "factor": -1, "flip_aug": false, "full_bf16": false, "full_fp16": false, "gradient_accumulation_steps": 1.0, "gradient_checkpointing": true, "keep_tokens": 1, "learning_rate": 0.0001, "lora_network_weights": "D:/lycoris/sdxl\\sdxl-kill bill, the bride-lora-1.0av2.safetensors", "lr_scheduler": "constant", "lr_scheduler_num_cycles": "1", "lr_scheduler_power": "", "lr_warmup": 0, "max_bucket_reso": 2048, "max_data_loader_n_workers": "0", "max_resolution": "1024,1024", "max_timestep": 1000, "max_token_length": "75", "max_train_epochs": "1", "mem_eff_attn": false, "mid_lr_weight": "", "min_bucket_reso": 64, "min_snr_gamma": 0, "min_timestep": 0, "mixed_precision": "bf16", "module_dropout": 0, "multires_noise_discount": 0, "multires_noise_iterations": 0, "network_alpha": 128, "network_dim": 128, "network_dropout": 0, "no_token_padding": false, "noise_offset": 0.0357, "noise_offset_type": "Original", "num_cpu_threads_per_process": 2, "optimizer": "AdamW", "optimizer_args": "", "persistent_data_loader_workers": false, "prior_loss_weight": 1.0, "random_crop": false, "rank_dropout": 0, "save_every_n_epochs": 1, "save_every_n_steps": 0, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_precision": "bf16", "scale_v_pred_loss_like_noise_pred": false, "scale_weight_norms": 0, "sdxl": true, "sdxl_cache_text_encoder_outputs": false, "sdxl_no_half_vae": true, "seed": "17415", "shuffle_caption": false, "stop_text_encoder_training_pct": 0, "text_encoder_lr": 0.0001, "train_batch_size": 1, "train_on_input": false, "training_comment": "trigger: portrait", "unet_lr": 0.0001, "unit": 1, "up_lr_weight": "", "use_cp": false, "log_with": "", "v2": false, "v_parameterization": false, "vae_batch_size": 0, "weighted_captions": false, "xformers": true}