{"LoRA_type": "LyCORIS/LoHa", "adaptive_noise_scale": 0, "additional_parameters": "", "block_alphas": "", "block_dims": "", "block_lr_zero_threshold": "", "bucket_no_upscale": true, "bucket_reso_steps": 1, "cache_latents": true, "cache_latents_to_disk": true, "caption_dropout_every_n_epochs": 0.0, "caption_dropout_rate": 0, "caption_extension": ".none-use-foldername", "clip_skip": "1", "color_aug": false, "conv_alpha": 4, "conv_alphas": "", "conv_dim": 8, "conv_dims": "", "decompose_both": false, "dim_from_weights": false, "down_lr_weight": "", "enable_bucket": true, "epoch": 2, "factor": -1, "flip_aug": false, "full_fp16": false, "gradient_accumulation_steps": 4, "gradient_checkpointing": false, "keep_tokens": "0", "learning_rate": 0.0001, "lora_network_weights": "", "lr_scheduler": "cosine", "lr_scheduler_num_cycles": "", "lr_scheduler_power": "", "lr_warmup": 0, "max_data_loader_n_workers": "0", "max_resolution": "512,512", "max_token_length": "75", "max_train_epochs": "", "mem_eff_attn": false, "mid_lr_weight": "", "min_snr_gamma": 10, "mixed_precision": "bf16", "module_dropout": 0, "multires_noise_discount": 0.2, "multires_noise_iterations": 8, "network_alpha": 16, "network_dim": 32, "network_dropout": 0, "no_token_padding": false, "noise_offset": 0, "noise_offset_type": "Multires", "num_cpu_threads_per_process": 2, "optimizer": "AdamW", "optimizer_args": "", "persistent_data_loader_workers": false, "prior_loss_weight": 1.0, "random_crop": false, "rank_dropout": 0, "save_every_n_epochs": 1, "save_every_n_steps": 0, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_precision": "fp16", "scale_v_pred_loss_like_noise_pred": false, "scale_weight_norms": 1, "seed": "", "shuffle_caption": false, "stop_text_encoder_training": 0, "text_encoder_lr": 0.0001, "train_batch_size": 1, "train_on_input": true, "training_comment": "", "unet_lr": 0.0001, "unit": 1, "up_lr_weight": "", "use_cp": true, "log_with": "", "v2": false, "v_parameterization": false, "vae_batch_size": 0, "weighted_captions": false, "xformers": true}