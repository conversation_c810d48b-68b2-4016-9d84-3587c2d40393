{"LoRA_type": "LyCORIS/LoKr", "LyCORIS_preset": "full", "adaptive_noise_scale": 0.005, "additional_parameters": "", "block_alphas": "", "block_dims": "", "block_lr_zero_threshold": "", "bucket_no_upscale": true, "bucket_reso_steps": 1, "cache_latents": true, "cache_latents_to_disk": true, "caption_dropout_every_n_epochs": 0, "caption_dropout_rate": 0.05, "caption_extension": ".txt", "clip_skip": 1, "color_aug": false, "constrain": 0, "conv_alpha": 1, "conv_block_alphas": "", "conv_block_dims": "", "conv_dim": 100000, "debiased_estimation_loss": false, "decompose_both": true, "dim_from_weights": false, "down_lr_weight": "", "enable_bucket": true, "epoch": 20, "factor": 6, "flip_aug": false, "full_bf16": false, "full_fp16": false, "gradient_accumulation_steps": 1, "gradient_checkpointing": false, "keep_tokens": "0", "learning_rate": 0.0001, "lora_network_weights": "", "lr_scheduler": "constant", "lr_scheduler_args": "", "lr_scheduler_num_cycles": "1", "lr_scheduler_power": "", "lr_warmup": 0, "max_bucket_reso": 2048, "max_data_loader_n_workers": "1", "max_grad_norm": 1, "max_resolution": "768,768", "max_timestep": 1000, "max_token_length": "75", "max_train_epochs": "", "max_train_steps": "113", "mem_eff_attn": false, "mid_lr_weight": "", "min_bucket_reso": 256, "min_snr_gamma": 5, "min_timestep": 0, "mixed_precision": "bf16", "module_dropout": 0, "multires_noise_discount": 0.3, "multires_noise_iterations": 10, "network_alpha": 1, "network_dim": 100000, "network_dropout": 0, "no_token_padding": false, "noise_offset": 0, "noise_offset_type": "Multires", "num_cpu_threads_per_process": 2, "optimizer": "AdamW", "optimizer_args": "\"weight_decay=0.1\" \"betas=0.9,0.99\"", "persistent_data_loader_workers": false, "prior_loss_weight": 1, "random_crop": false, "rank_dropout": 0, "rank_dropout_scale": true, "rescaled": false, "save_every_n_epochs": 0, "save_every_n_steps": 29, "save_last_n_steps": 0, "save_last_n_steps_state": 0, "save_precision": "fp16", "scale_v_pred_loss_like_noise_pred": false, "scale_weight_norms": 1, "sdxl": false, "sdxl_cache_text_encoder_outputs": false, "sdxl_no_half_vae": true, "seed": "1234", "shuffle_caption": false, "stop_text_encoder_training": 0, "text_encoder_lr": 0.0001, "train_batch_size": 1, "train_norm": true, "train_on_input": true, "training_comment": "busty blonde woman full", "unet_lr": 0.0001, "unit": 1, "up_lr_weight": "", "use_cp": true, "use_scalar": false, "use_tucker": false, "log_with": "", "v2": false, "v_parameterization": false, "v_pred_like_loss": 0, "vae": "", "vae_batch_size": 0, "weighted_captions": false, "xformers": "xformers"}