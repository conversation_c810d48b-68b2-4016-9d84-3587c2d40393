# Flexible PyTorch requirements - uses existing PyTorch installation
# Custom index URL for specific packages
--extra-index-url https://download.pytorch.org/whl/cu128

# Use existing PyTorch installation (skip torch and torchvision)
# torch>=2.0.0  # commented out to use existing installation
# torchvision>=0.15.0  # commented out to use existing installation
xformers>=0.0.30

-r requirements_windows.txt
