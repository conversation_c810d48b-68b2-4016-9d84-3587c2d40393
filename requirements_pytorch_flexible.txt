# Flexible PyTorch requirements - uses existing PyTorch installation
# Custom index URL for specific packages
--extra-index-url https://download.pytorch.org/whl/cu128

# Use existing PyTorch installation (skip torch and torchvision)
# torch and torchvision are commented out to use existing installation
# torch>=2.7.0
# torchvision>=0.22.0

# Only install additional packages that are safe to install
xformers>=0.0.30

-r requirements_windows.txt
